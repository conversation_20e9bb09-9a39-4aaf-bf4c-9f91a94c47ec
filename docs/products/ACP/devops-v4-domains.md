# DevOps v4 Domains

## Overview

| Feature                                                           | ACP DevOps v4                                                    | Harness                                   | O<PERSON>                                                                                                                                                                                               |
|:----------------------------------------------------------------- |:----------------------------------------------------------------:|:-----------------------------------------:|:-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|
| [Code repository](#code-repository)                               | Gitlab CE                                                        | `Harness Code`                            | N/A                                                                                                                                                                                               |
| [Artifact repository](#artifact-repository)                       | Harbor + Registry + Nexus                                        | `Harness Artifacts`                       | `Registry` + `Quay`                                                                                                                                                                               |
| [Continuous Integration / Build](#continuous-integration)         | `ACP Shipwright`+  DevOps `Tekton Pipeline` / `Pipeline-as-code` | `Harness CI Pipelines`                    | `Shipwright` + `Build` (Deprecated)                                                                                                                                                               |
| [Continuous Delivery / Pipelines](#continuous-delivery--gitops)   | `Tekton Pipeline` / `Pipeline-as-code` + `ArgoCD`                | `Harness Pipelines` + `ArgoCD` + `FluxCD` | `Tekton Pipeline` / `Pipeline-as-code` + `ArgoCD`+`ArgoRollouts`                                                                                                                                  |
| [Feature Flags](#feature-flags)                                   | ?                                                                | `Harness Feature Flags`                   | ?                                                                                                                                                                                                 |
| [Security Testing Orchestration](#security-testing-orchestration) | ?                                                                | `Harness Security Testing`                | ?                                                                                                                                                                                                 |
| [Supply Chain Security](#supply-chain-security)                   | `Tekton Chains`                                                                | `Harness Supply Chain Security`           | `Red Hat Advanced Cluster Security (StackRox)`                                                                                                                                                    |
| [Chaos Engineering](#chaos-engineering)                           | ?                                                                | `Harness Chaos Engineering`               | ?                                                                                                                                                                                                 |
| [Service Reliability Management](#service-reliability-management) | ?                                                                | `Harness SRM`                             | ?                                                                                                                                                                                                 |
| [Internal Developer Portal](#internal-developer-portal)           | `DP`                                                             | `Harness IDP`                             | `Red Hat Developer Hub` / `Janus`                                                                                                                                                                 |
| [Software Engineering Insights](#software-engineering-insights)   | ?                                                                | `Harness Insights`                        | [`OpenShift Insights`](https://docs.openshift.com/container-platform/4.17/support/remote_health_monitoring/using-insights-operator.html) + `GitOps` + `Tekton Pipelines` + `Prometheus + Grafana` |
| [Incident Response](#incident-response)                           | ?                                                                | `Harness Incident Response`               | ?                                                                                                                                                                                                 |
| [Database DevOps](#database-devops)                               | ?                                                                | `Harness DB DevOps`                       | ?                                                                                                                                                                                                 |
| [Cloud Development Environments](#cloud-development-environments) | ?                                                                | `Gitspaces`                               | [`Red Hat OpenShift Dev Spaces`](https://developers.redhat.com/products/openshift-dev-spaces/overview)                                                                                            |
| [Infrastructure As Code](#infrastructure-as-code)                 | ?                                                                | `Terraform` (OpenTofu)                    | ?                                                                                                                                                                                                 |
| [Cloud Cost Management](#cloud-cost-management)                   | `ACP OpenCost`                                                   | `Harness Cloud Cost Management`           | [`Red Hat Cost Management Operator`](https://cloud.redhat.com/learning/learn:cost-management-azure-red-hat-openshift/resource/resources:what-is-cost-management)                                  |
| [AI Development Assistant](#ai-native-development-assistant)      | `ACP Documentation Assistent `                                                                | `Harness AI Development Assistant`        | `LightSpeed` + `Openshift AI`                                                                                                                                                                     |

## Harness

[Roadmap](https://developer.harness.io/roadmap)

## OCP

- OCP DevOps https://developers.redhat.com/learn/openshift/devops-openshift
- Operators https://access.redhat.com/support/policy/updates/openshift_operators

## DevOps Features / Domains

### Continuous Integration

**Harness**

- Site: https://www.harness.io/products/continuous-integration
- Docs: https://developer.harness.io/docs/continuous-integration
- Roadmap: https://developer.harness.io/roadmap/#ci

_Highlights_:

- **CI Test Inteligence:** https://www.harness.io/products/continuous-integration/test-intelligence
- **Build cache:** https://www.harness.io/products/continuous-integration/incremental-builds
- **AI and templates:** https://www.harness.io/products/ai-native-software-delivery#ai-devops-assistant
- **CI Cloud:** https://www.harness.io/products/continuous-integration/ci-cloud

**OpenShift**

- [`Red Hat OpenShift Pipelines`](https://docs.redhat.com/en/documentation/red_hat_openshift_pipelines/1.17):  Tekton + Pipeline-as-Code
- `OpenShift Build` (Deprecated): OCP 内置 s2i 功能
- [`Builds for Red Hat OpenShift`](https://docs.redhat.com/en/documentation/builds_for_red_hat_openshift/1.3): Shipwright

**ACP DevOps**

- DevOps `Tekton Pipeline` + `Pipeline-as-Code`
- Container Platform `Shipwright/S2i`

### Continuous Delivery + GitOps

**Harness**

- Site: https://www.harness.io/products/continuous-delivery
- Docs:  https://developer.harness.io/docs/continuous-delivery
- Roadmap: https://developer.harness.io/roadmap/#cd

_Highlights_:

- 开箱即用部署策略：Canary, blue/green, rolling update [了解更多](https://developer.harness.io/docs/continuous-delivery/manage-deployments/deployment-concepts)
- 自定义部署类型： [了解更多](https://developer.harness.io/docs/continuous-delivery/deploy-srv-diff-platforms/custom/custom-deployment-tutorial)
- 模板化： [了解更多](https://developer.harness.io/docs/category/templates)
- ephemeral environments:  Deploy to ephemeral environments, or use your CD pipelines to provision long-lived environments (Infrastructure-as-code integration) [了解更多](https://developer.harness.io/docs/continuous-delivery/cd-infrastructure/provisioning-overview)
- GitOps: ArgoCD + Flux, 流水线集成，高度自动化（PR, sync, rollback）   [了解更多](https://www.harness.io/products/continuous-delivery/harness-gitops)
- AI-assisted CD： monitor deployment health, rollback automation, smart notification [视频](https://youtu.be/bp8GXAT_SqM?list=TLGGIRWtakTiUUUwMzAzMjAyNQ)  [了解更多](https://www.harness.io/products/continuous-delivery/ai-assisted-deployment-verification)
- Policy (OPA): deployment freeze, RBAC, audit, template library [了解更多](https://www.harness.io/products/continuous-delivery/devops-pipeline-governance)
- Metrics/DORA: 开箱即用的 DORA metrics [了解更多](https://www.harness.io/products/continuous-delivery/cd-visualize-devops-data)
- 集成生态：很多现成的集成

**OpenShift**

- [`Red Hat OpenShift Pipelines`](https://docs.redhat.com/en/documentation/red_hat_openshift_pipelines/1.17):  Tekton + Pipeline-as-Code
- [`Red Hat OpenShift GitOps`](https://docs.redhat.com/en/documentation/red_hat_openshift_gitops/1.15): ArgoCD + Argo Rollouts

**ACP DevOps**

- DevOps `Tekton Pipeline` + `Pipeline-as-Code`
- Container Platform GitOps (ArgoCD)

- Argo Rollouts?

### Code repository

**Harness**

- Site: ?
- Docs: https://developer.harness.io/docs/code-repository
- Roadmap: https://developer.harness.io/roadmap/#code

_Highlights_:

- Harness AIDA Semantic Code Search: Natural language to search codebase: https://developer.harness.io/docs/code-repository/work-in-repos/semantic-search
- Harness AIDA PR summary: automatted PR summary https://developer.harness.io/docs/code-repository/pull-requests/aida-code-pr
- Scan: Secrets, vulnerabilities, OPA policies: https://developer.harness.io/docs/code-repository/config-repos/security

**OpenShift**

???

**ACP DevOps**

- Gitlab Operator
- Connector (Git)

### Artifact Registry

:new:

**Harness**

- Site: https://www.harness.io/products/artifact-registry
- Docs: https://developer.harness.io/docs/artifact-registry
- Roadmap: ?

_Highlights_:

- Universal registry:
  - supported: Docker, helm, generic, maven, gradle, SBT (scala build tool)
  - coming soon: npm, python, go, nuget, debian, rust, rpm, ruby, conan
- Advanced search: Harness AIDA search
- Security Compliance: 制品漏洞展示跟踪 [link](https://www.harness.io/products/security-testing-orchestration)
- Supply Chain Security integration: SBOM, policies, provenances and attestations [link](https://www.harness.io/products/software-supply-chain-assurance)
- e2e lifecycle management: traceability, cleanup, etc

**OpenShift**

- Quay: https://docs.redhat.com/en/documentation/red_hat_quay/3.13

**ACP DevOps**

- Harbor
- Nexus
- Connector (OCI)

### Software Engineering Insights

**Harness**

- Site: https://www.harness.io/products/software-engineering-insights
- Docs: https://developer.harness.io/docs/software-engineering-insights
- Roadmap: https://developer.harness.io/roadmap/#sei

_Highlights_:

- Productivy Scores
  - Developer Insights: SCM reports help you analyze activity in your SCM tools
  - Trellis scores:  Trellis Scores are a proprietary productivity metric developed by SEI that provides a holistic understanding of software development team and individual performance. These scores are calculated based on a combination of key factors, including Code Quality, Code Volume, Speed, Impact, Proficiency, and Collaboration
- DORA Metrics
- Agile Metrics:
  - Sprint metrics can help you plan and deliver on sprints more effectively, including backlog grooming and story hygiene
  - Lead time metrics
  - Velocity metrics
  - Issue tracking
  - CI/CD insights
  - Flow metrics
  - Issue hygiene metrics
  - Support hygiene report
  - Epic insights
- Quality
  - PagerDuty reports
  - SonarQube reports
- Investment Analysis: business alignment report
- Goals Tracking
- AI Usage insights: Sentiment analysis (feedback based), sentiment & qualitative analysis (scm integration)

**OpenShift**

- Insights: https://docs.redhat.com/en/documentation/red_hat_insights_overview/1-latest

**ACP DevOps**

???

### Supply Chain Security

**Harness**

- Site: https://www.harness.io/products/supply-chain-security
- Docs: https://developer.harness.io/docs/software-supply-chain-assurance
- Roadmap: https://developer.harness.io/roadmap/#ssca

_Highlights_:

- Policy-based governance: Artifact chain of custody

  - Artifact promotion with SLSA build attestation
  - Audit trail
  - control usage of OSS dependencies
  - SBOM drift detection

- Zero-day vulnerability response:

  - Block zero-day vulnerabilities: block builds using OPA policies
  - track remediation: Create trackers for risk & compliance of vulnerabilities in dependencies

**OpenShift**

- Red Hat Advanced Cluster Security???: https://docs.redhat.com/en/documentation/red_hat_advanced_cluster_security_for_kubernetes/4.6
- Tekton Chains??

**ACP DevOps**

- Tekton Chains
- NeuVector (AIT)
- StackRox (AIT)

### AI Native Software Delivery

:new:

**Harness**

- [AI DevOps Assistant](https://www.harness.io/products/ai-native-software-delivery#ai-devops-assistant)
- [AI QA Assistant](https://www.harness.io/products/ai-native-software-delivery#ai-qa-assistant)
- [AI Code Assistant](https://www.harness.io/products/ai-native-software-delivery/ai-code-agent)

_Highlights_:

- Code Search: natural language code search
- Artifact Search:  natural language artifact search
- AI Code agent: generate code, comments, tests, terraform scripts, PR summaries
- AI Assistent onboarding: SDK, code snippets, etc.
- AI test automation:
- AI DevOps assistant: generate pipelines, pipeline refactoring, proactive suggestions, automatic diagnosis and remediation of common failures
- AI FinOps agent: generate cloud policies to manage cloud resources
- Assisted policy writing: Create customized OPA policies efficiently, minimizing manual effort and focusing on strategic solutions.
- Vulnerability remediations: Identify and implement smart remediation strategies
- ChaosGuard: Define and control chaos experiments across all environments with stringent security governance, ensuring resilience and innovation in a secure, efficient manner.
- Dashboard assistent: Help generating/improving dashboards
- Harness support: chat AI support assistent

**Harness AI Roadmap**

![harness-ai-roadmap](assets/harness-ai-roadmap.png){width=600}

**OpenShift**

- LightSpeed: https://docs.redhat.com/en/documentation/red_hat_openshift_lightspeed/1.0tp1
- OpenShift AI: https://docs.redhat.com/en/documentation/red_hat_openshift_ai/2025

_LightSpeed_:

> Red Hat OpenShift Lightspeed generates answers to questions based on the content in the official OpenShift Container Platform product documentation

- Builds for Red Hat OpenShift
- Red Hat Advanced Cluster Security for Kubernetes
- Red Hat Advanced Cluster Management for Kubernetes
- Red Hat CodeReady Workspaces
- Red Hat OpenShift GitOps
- Red Hat OpenShift Pipelines
- Red Hat OpenShift Serverless
- Red Hat OpenShift Service Mesh 3.x
- Red Hat Quay

**ACP DevOps**

???

### Cloud Development environments

:new:

**Harness**

- Site: https://www.harness.io/products/cloud-development-environments
- Docs: https://developer.harness.io/docs/cloud-development-environments
- Roadmap: ?

> Cloud Development Environments enable instant coding from any location, improve collaboration, accelerate onboarding, and enhance security with centralized control and consistent setups for seamless productivity.

_Highlights_:

- Gitspaces: run locally or remotely, on-demand development environments. Integrate with existing workflows
- Environment template updates
- AI code assistant: generate code, comments, tests, automatic code quality checks etc.
- multiple IDE support:
  - VS Code Desktop
  - VS Code Browser
  - IntelliJ IDEA
- Integrates with artifact registry connectors to pull images

**OpenShift**

- Dev Spaces: https://docs.redhat.com/en/documentation/red_hat_openshift_dev_spaces/3.18

_Highlights_:

- Gitspaces like
- IDEs: [List](https://docs.redhat.com/en/documentation/red_hat_openshift_dev_spaces/3.18/html/user_guide/ides-in-workspaces#ides-in-workspaces-_supported_ides)
  - Visual Studio Code - Open Source
- Default developer image (UDI): Common set of tools for development
- Kubedock: Podman/Docker like experience [Kubedock](https://github.com/joyrex2001/kubedock)
- Tests in [Testcontainers](https://testcontainers.com/)

**ACP DevOps**

???

---

### Security Testing Orchestration

**Harness**

- Site: https://www.harness.io/products/security-testing-orchestration
- Docs: https://developer.harness.io/docs/security-testing-orchestration
- Roadmap: https://developer.harness.io/roadmap/#sto

**OpenShift**

???

**ACP DevOps**

???

### Chaos Engineering

**Harness**

- Site: https://www.harness.io/products/chaos-engineering
- Docs: https://developer.harness.io/docs/chaos-engineering
- Roadmap: https://developer.harness.io/roadmap/#ce

**OpenShift**

[Kraken???](https://github.com/krkn-chaos)

**ACP DevOps**

???

### Service Reliability Management

**Harness**

- Site: https://www.harness.io/products/service-reliability-management
- Docs: https://developer.harness.io/docs/service-reliability-management
- Roadmap: https://developer.harness.io/roadmap/#srm

**OpenShift**

???

**ACP DevOps**

???

### Internal Developer Portal

**Harness**

- Site: https://www.harness.io/products/internal-developer-portal
- Docs: https://developer.harness.io/docs/internal-developer-portal
- Roadmap: https://developer.harness.io/roadmap/#idp

**OpenShift**

- Red Hat Developer Hub
- Janus

**ACP DevOps**

- Developer Portal

### Incident Response

:new:
**Harness**

- Site: https://www.harness.io/products/incident-response
- Docs: https://developer.harness.io/docs/incident-response
- Roadmap: ?

**OpenShift**

???

**ACP DevOps**

???

### Infrastructure-as-Code

**Harness**

- Site: https://www.harness.io/products/infrastructure-as-code-management
- Docs: https://developer.harness.io/docs/infrastructure-as-code-management/
- Roadmap: https://developer.harness.io/roadmap/#iacm

**OpenShift**

???

**ACP DevOps**

???

---

### Feature Flags

**Harness**

*Feature Flags*

- Site: https://www.harness.io/products/feature-flags
- Docs: https://developer.harness.io/docs/feature-flags/
- Roadmap: https://developer.harness.io/roadmap/#ff

*Feature Experiments* :new:

- Site: https://www.harness.io/products/feature-management-experimentation
- Docs: ?
- Roadmap: https://developer.harness.io/roadmap/#fme

**OpenShift**

???

**ACP DevOps**

???

### Database DevOps

:new:

**Harness**

- Site: https://www.harness.io/products/database-devops
- Docs: https://developer.harness.io/docs/database-devops
- Roadmap: ?

**OpenShift**

???

**ACP DevOps**

???

### Cloud Cost Management

**Harness**

- Site: https://www.harness.io/products/cloud-cost-management
- Docs: https://developer.harness.io/docs/cloud-cost-management
- Roadmap: https://developer.harness.io/roadmap/#ccm

**OpenShift**

- Cost Management: https://docs.redhat.com/en/documentation/cost_management_service/1-latest

**ACP DevOps**

- Cost Management (AIT/CP)

## DevOps v4

### 领域优先级

| P0                                               | P1                                             | P2                                              | Reason                   |
| ------------------------------------------------ | ---------------------------------------------- | ----------------------------------------------- | ------------------------ |
| :red_circle: CI + CD + GitOps                    |                                                |                                                 | 核心场景                     |
| :red_circle: Artifact Registry + Code Repository |                                                |                                                 | 安全串联                     |
| :red_circle: Insights                            |                                                |                                                 | 研发效能度量                   |
| :red_circle: Supply Chain Security               |                                                |                                                 | 安全                       |
| :red_circle: AI Native Software Delivery         |                                                |                                                 | AI                       |
|                                                  | :orange_circle: Cloud Development Environments |                                                 | 补齐 OCP 能力                |
|                                                  | :orange_circle: Security Testing Orchestration |                                                 | 安全                       |
|                                                  | :orange_circle: Chaos Engineering              |                                                 | 解决脆弱性                    |
|                                                  | :orange_circle: Infrastructure as Code         |                                                 | 服务质量<br/>监控/告警           |
|                                                  |                                                | :yellow_circle: Service Reliability Management  | 故障处理<br/>应用智能运维<br/>AI相关 |
|                                                  |                                                | :yellow_circle: Incident Response               | 国外核心场景                   |
|                                                  |                                                | :yellow_circle: Feature Flags / experimentation | TBD                      |
|                                                  |                                                | :yellow_circle: Database DevOps                 | TBD                      |
|                                                  |                                                | :yellow_circle: Cloud cost management           | 属于其他团队的                  |

### 领域的演进过程

1. **方案优先**: 基于社区已有的项目提供功能使用场景的方案
2. **生态**: 根据使用反馈和需求规划纳入组建/项目到产品生态中（安全整改）
3. **产品化**: 产品自带的组建，产品UI支持

```mermaid
graph LR
  1.方案 --> 2.生态
  2.生态 --> 3.产品化
```

#### 1. 方案

通过了解并使用现成的工具/开源项目输出可落地目标的方案

**过程 🛣️**

1. **了解目标**：通过学习方法论和竟品了解领域所需要达成的目标/成功使用场景
2. **调研**：调研能够满足目标的项目/工具，并输出一个 demo 的提案
3. **实现**：根据提案实现 demo 并在团队内验收修正
4. **交付方案**：提供 s1/s2 方案，考虑是否添加到产品文档中，并发布到 Alauda Cloud，跟进落地
5. 生态组建（可选）：提供 s0 级别的组建纳入到生态
6. **AI结合使用**：智能文档 / AI API 使用

**交付件 📦**

- **方案**：具体的落地方案的步骤
- **demo 示例**：通过方案能明确落地方案所满足的目标
- 组建（可选）：同上

**风险 ❗**

- 文档质量把控：目前公司内没有非常清晰的控制文档质量把控，会依靠开发+测试配合以及团队验收
- oncall支持情况：交付的方案可能会引起更多的 oncall 毕竟没有产品化
- 项目支持：因为不交付制品可能会存在客户环境落不了地的风险

#### 2. 生态

根据方案使用和落地反馈推进相关的组建/项目纳入到产品生态中 （参考 [平台生态组件支持等级](https://confluence.alauda.cn/pages/viewpage.action?pageId=257263545)）

**过程 🛣️**

1. **需求**：给生态团队提需求或者一起合作纳入组建到生态中
2. **等级评估**：根据客户需求，竞品评估考虑支持的等级 (s0, s1, s2)
3. **方案变更**： 评估并执行适配生态组建的方案变更

**交付件 📦**

- **生态组建**：纳入项目到生态中，并确保方案能持续使用

**风险 ❗**

- 生态团队支持：无法短时间支持所需要的组建

#### 3. 产品化

经过需求，市场分析，竞品分析决定需要纳入到产品迭代周期中，通过产品功能/UI提供更好的体验/使用场景，以及提升对整体方案的支持

**过程 🛣️**

1. **设计**：通过明确目标场景和评估，输出可产品化的设计思路
2. **内部评审**：对输出的设计思路进行评审
3. **功能排期和开发**： 进入正常的产品的迭代流程，并提升支持等级（s3，s4）

**交付件 📦**

- **产品组建**：通过提升等级输出产品包含的组建

- **产品文档**：对应的组建/项目的产品文档

- **UI（可选）**：提供产品的 UI 体验

- **质量把控方案**： 输出组建以及产品的质量把控方案

  - 项目自动化测试：开源项目的自动化测试使用落地
  - 产品功能 integration：功能使用的功能自动化集成测试
  - 产品发版 e2e：端到端的测试用例（自动化可选）
  - 安全测试用例
  - 非功能：（beta）稳定性，性能，高可用

- **生产可用**

  - 备份恢复方案
  - 容灾方案
  - 运维手册：监控面板，日志，告警（可选），故障处理

**风险 ❗**

- 团队规模：纳入新的组建导致的团队整体维护成本和节奏收比较大的影响
- 知识负荷：从组建的运维，安全漏洞修复，产品化所有的要求新组建的纳入要求团队的扩展

## 2025 Q2-Q3 目标

分四个阶段：

1. 海外新客户客户能使用 v4

2. 能使用方案的新客户

3. 所有新客户

4. 从v3迁移到v4的客户

**尽快改善v4代替v3**

- 构建/发布流水线
- 工具集成绑定
- 工具部署运维
- 制品清理 / 制品晋级
- 概览/统计/度量
- 垂直领域：代码，制品，测试，项目 (考虑有否需要做的)

**步骤：**

- v3/v4 功能对比
- 通过方案快提供代替方案
- 功能开发/测试
- 迁移方案
- v3/v4 demo

## 2025 Q4～2026 Q1 目标

- 若干领域的调研验证
- 演进产品