# language: zh-cn
@cicd
@tekton-pipelines
@tekton-pipelines-run-pipeline
功能: 执行Tekton流水线

  作为一名开发者
  我希望能够指定流水线的参数和工作空间
  并且运行流水线
  以便我可以满足流水线的多使用方法

  背景:
      假定 已经登录认证
      并且 已经选择了一个特定的命名空间
      并且 有权限"查看"该命名空间中的"流水线"
      并且 有权限"创建"该命名空间中的"流水线执行记录"
      并且 该命名空间中有流水线

  @tekton-run-pipeline-001
  @allure.label.case_id:tekton-run-pipeline-001
  @automateable
  @smoke
  @priority-high
  场景: 基于指定的流水线执行
    假定 指定已存在流水线来执行
    当 开启执行流水线流程
    那么 运行流水线的流程展示
    并且 指定的流水线已经选中
    并且 选中的流水线不能更改

  @tekton-run-pipeline-002
  @allure.label.case_id:tekton-run-pipeline-002
  @automateable
  @priority-middle
  场景大纲: 指定流水线参数 - string
    假定 指定已存在流水线来执行
    并且 已经开启执行流程
    并且 已经能看到流水线参数
    当 用户指定参数 <param> 值为 <value>
    那么 流水线执行记录的 <param> 参数值为 <value>
    例子:
       | param  | value  |
       | string | "abc" |
       | string | ""    |

  @tekton-run-pipeline-003
  @allure.label.case_id:tekton-run-pipeline-003
  @automateable
  @priority-middle
  场景大纲: 指定流水线参数 - array
    假定 指定已存在流水线来执行
    并且 已经开启执行流程
    并且 已经能看到流水线参数
    当 用户指定参数 <param> 值为 <value>
    那么 流水线执行记录的 <param> 参数值为 <value>
    例子:
       | param  | value  |
       | array | ["abc", "def"] |
       | array | []    |


  @tekton-run-pipeline-004
  @allure.label.case_id:tekton-run-pipeline-004
  @automateable
  @priority-middle
  场景大纲: 指定流水线参数 - object
    假定 指定已存在流水线来执行
    并且 已经开启执行流程
    并且 已经能看到流水线参数
    当 用户指定参数 <param> 值为 <value>
    那么 流水线执行记录的 <param> 参数值为 <value>
    例子:
       | param  | properties                                  | value                                                              |
       | object | {"firstProperty": {}, "secondProperty": {}} | {"firstProperty": "firstValue", "secondProperty": "secondValue"}   |
       | object | {"name": {}}                                | {"name": ""}                                                       |



  @tekton-run-pipeline-005
  @allure.label.case_id:tekton-run-pipeline-005
  @automateable
  @priority-middle
  场景大纲: 指定流水线工作区 - volumeClaimTemplate
    假定 指定已存在流水线来执行
    并且 已经开启执行流程
    并且 已经能看到流水线工作区
    并且 流水线中有 workspace <source> 可选为 <optional>
    当 指定工作区 <workspace> 为 <type> 类型
    并且 访问模式为 <accessMode>
    并且 容量为 <storage>
    # 张开这表单选项
    那么 流水线执行记录的 <workspace> 工作区类型为 <type> 配置为 <config>
    例子:
       | workspace    | optional  | type                | accessMode    | storage     | config                                                                                                                      |
       | source       | false     | volumeClaimTemplate | ReadWriteOnce | 1Gi         | {"volumeClaimTemplate":{"metadata":{"creationTimestamp":null},"spec":{"accessModes":["ReadWriteOnce"],"resources":{"requests":{"storage":"1Gi"}}}}} |
       | source       | false     | volumeClaimTemplate | ReadWriteMany | 10Gi        | {"volumeClaimTemplate":{"metadata":{"creationTimestamp":null},"spec":{"accessModes":["ReadWriteMany"],"resources":{"requests":{"storage":"10Gi"}}}}} |
       | source       | false     | volumeClaimTemplate | ReadOnlyMany  | 1000Mi      | {"volumeClaimTemplate":{"metadata":{"creationTimestamp":null},"spec":{"accessModes":["ReadOnlyMany"],"resources":{"requests":{"storage":"1000Mi"}}}}} |


  @tekton-run-pipeline-005
  @allure.label.case_id:tekton-run-pipeline-005
  @automateable
  @priority-middle
  场景大纲: 指定流水线工作区 - persistentVolumeClaim
    假定 指定已存在流水线来执行
    并且 已经开启执行流程
    并且 已经能看到流水线工作区
    并且 流水线中有 workspace <source> 可选为 <optional>
    当 指定工作区 <workspace> 为 <type> 类型
    并且 PVC存储为 <pvc>
    并且 子目录为 <subPath>
    那么 流水线执行记录的 <workspace> 工作区类型为 <type> 配置为 <config>
    例子:
       | workspace   | optional  | type                  | pvc          | subPath | config                                                                |
       | cache       | true      | persistentVolumeClaim | cache-pvc    | cache   | {"persistentVolumeClaim":{"claimName":"cache-pvc"},"subPath":"cache"} |
       | cache       | true      | persistentVolumeClaim | another-pvc  |         | {"persistentVolumeClaim":{"claimName":"another-pvc"}}                 |


  @tekton-run-pipeline-006
  @allure.label.case_id:tekton-run-pipeline-006
  @automateable
  @priority-middle
  场景大纲: 指定流水线工作区 - secret
    假定 指定已存在流水线来执行
    并且 已经开启执行流程
    并且 已经能看到流水线工作区
    并且 流水线中有 workspace <source> 可选为 <optional>
    当 指定工作区 <workspace> 为 <type> 类型
    并且 Secret为 <secret>
    并且 项为 <items>
    那么 流水线执行记录的 <workspace> 工作区类型为 <type> 配置为 <config>
    例子:
       | workspace   | optional  | type      | secret     | items                                               | config                                |
       | secret      | true      | secret    | my-secret  |                                                     | {"secret":{"secretName":"my-secret"}} |
       | secret      | true      | secret    | basic-auth | [{"username": "user.txt", "password": "pass.txt"}]  | {"secret":{"secretName":"basic-auth","items":[{"username": "user.txt", "password": "pass.txt"}]}} |


  @tekton-run-pipeline-007
  @allure.label.case_id:tekton-run-pipeline-007
  @automateable
  @priority-middle
  场景大纲: 指定流水线工作区 - configMap
    假定 指定已存在流水线来执行
    并且 已经开启执行流程
    并且 已经能看到流水线工作区
    并且 流水线中有 workspace <source> 可选为 <optional>
    当 指定工作区 <workspace> 为 <type> 类型以及 <config> 配置
    那么 流水线执行记录的 <workspace> 工作区类型为 <type> 配置为 <config>
    例子:
       | workspace   | optional  | type         |  configMap     | items                                             |  config                                                                                        |
       | config      | true      | configMap    |  my-configmap  |                                                   | {"configMap":{"name":"my-configmap"}}                                                          |
       | config      | true      | configMap    |  second-config | [{"file1": "file.txt", "config": "config.yaml"}]  | {"configMap":{"name":"my-configmap","items":[{"file1": "file.txt", "config": "config.yaml"}]}} |



  @tekton-run-pipeline-008
  @allure.label.case_id:tekton-run-pipeline-008
  @automateable
  @priority-middle
  场景大纲: 指定流水线工作区 - emptyDir
    假定 指定已存在流水线来执行
    并且 已经开启执行流程
    并且 已经能看到流水线工作区
    并且 流水线中有 workspace <source> 可选为 <optional>
    当 指定工作区 <workspace> 为 <type> 类型
    那么 流水线执行记录的 <workspace> 工作区类型为 <type> 配置为 <config>
    例子:
       | workspace   | optional  | type         | config                                |
       | config      | true      | emptyDir     | {}                                    |


  @tekton-run-pipeline-009
  @allure.label.case_id:tekton-run-pipeline-009
  @automateable
  @priority-middle
  场景: 表单转YAML
      假定 指定已存在流水线来执行
      并且 已经开启执行流程
      并且 已经配置参数和工作区
      当 表单转编辑YAML
      那么 会展示流水线执行记录的YAML
      并且 所有配置保持不变

  @tekton-run-pipeline-010
  @allure.label.case_id:tekton-run-pipeline-010
  @automateable
  @priority-middle
  场景: YAML转表单
      假定 指定已存在流水线来执行
      并且 已经开启执行流程
      并且 已经配置参数和工作区
      并且 已经在编辑流水线执行记录YAML
      ## TODO：自动化时可以具体化例子 （考虑yaml执行）
      并且 YAML中修改了某个参数的值
      当 YAML转表单
      那么 会验证流水线执行记录YAML合法性
      并且 能够看到执行流水线的表单
      并且 所有配置保持不变


  @tekton-run-pipeline-011
  @allure.label.case_id:tekton-run-pipeline-011
  @automateable
  @priority-low
  场景: 不合法的YAML转图形化
      假定 指定已存在流水线来执行
      并且 已经开启执行流程
      并且 已经配置参数和工作区
      并且 在编辑一个不合法的流水线执行记录的YAML
      当 YAML转表单
      那么 会验证流水线执行记录YAML合法性
      并且 提示YAML不合法并保留在YAML编辑


  @tekton-run-pipeline-012
  @allure.label.case_id:tekton-run-pipeline-012
  @automateable
  @priority-high
  场景: 表单执行流水线
    假定 指定已存在流水线来执行
    并且 已经开启执行流程
    并且 已经配置参数和工作区
    当 开始执行流水线
    那么 流水线应该开始执行
    并且 能够看到流水线执行记录详情
    并且 流水线执行记录关联了选中的流水线


  @tekton-runpipeline-013
  @allure.label.case_id:tekton-run-pipeline-013
  @automateable
  @priority-high
  ## TODO: 补充内容
  场景: YAML执行流水线
    假定 指定已存在流水线来执行
    并且 已经在编辑流水线执行记录YAML
    并且 已经输入了流水线执行记录YAML
        """
        kind: PipelineRun
        """
    当 开始执行流水线
    那么 流水线应该开始执行
    并且 能够看到流水线执行记录详情


  @tekton-run-pipeline-013
  @allure.label.case_id:tekton-run-pipeline-013
  @automateable
  @priority-middle
  场景: 取消执行流水线
    假定 指定已存在流水线来执行
    并且 已经开启执行流程
    当 取消执行流水线
    那么 执行流水线流程退出
    并且 流水线不应该被执行

  @tekton-run-pipeline-014
  @allure.label.case_id:tekton-run-pipeline-014
  @automateable
  @priority-high
  场景: 基于 触发配置 运行指定流水线
    假定 已存在触发配置 "template" 和关联流水线 "pipe-template"
    当 执行流水线 pipe-template
    当 展示 关联的触发配置  "template"
    并且  鼠标悬停在 "template" 模板上，点击 "点击写入触发模版"
    那么 流水线相关参数被覆盖为触发配置中的参数
    并且 可成功执行流水线

  @tekton-run-pipeline-015
  @allure.label.case_id:tekton-run-pipeline-015
  @priority-middle
  场景: 验证 触发模版 清空
    当 点击 "清空触发模版写入的参数"
    那么 流水线相关的参数被清空

  @tekton-run-pipeline-016
  @allure.label.case_id:tekton-run-pipeline-016
  @priority-middle
  场景: 保存 触发模版
    当 点击 "保存触发模版" 按钮
    那么 弹出保存触发模版弹窗页
       | 标题  | 保存触发模版  |
       | 名称 | abc |
       | 描述 |  流水线保存的触发模版    |
       | 标签  | test：template1  |
       | 注解 | test：template2  |
       | 保存和取消按钮 |
    当 输入以上信息后，点击保存按钮
    那么 触发模版被成功保存
