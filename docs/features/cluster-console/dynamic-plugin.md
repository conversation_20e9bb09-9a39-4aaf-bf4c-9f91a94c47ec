# 动态插件部署和打包方案

## TL；DR

通过若干插件部署静态文件服务来提供动态插件的内容

[参考 OCP](https://github.com/openshift-pipelines/console-plugin/blob/main/charts/openshift-console-plugin/templates/deployment.yaml)

## 问题

当前 ACP 的动态UI插件方案非常简单不过对使用者体验并不理想

1. operator中声明UI插件镜像
2. alauda-console通过查询安装过的operator定期的获取 csv 中的前端插件镜像
3. 通过添加 configmap 配置以及动态获取所有的静态文件到alauda-console容器中并统一服务


这个模式存在的问题：

1. 延迟：因为需要存在 global 中的 alauda-console，从查询到保存整个过程又是定期的查询，从operator部署到实际能提供UI能努力有比较大的延迟，很多时候看起来是一个bug
2. 版本管理：多个集群不同的插件版本也出现需要不断的在修改alauda-console的容器内容，涉及到alauda-console自身需要对插件的版本有一定的规定
3. 稳定性和耦合性：如果 alauda-console 出现任何问题需要从头再此拷贝所有的静涛文件到自己的容器中，如果客户集群多插件安装的又多整个过程非常缓慢


## OCP 调研

### OCP console 和 console-operator

通过研究 OCP 是如何解决动态UI插件的问题有发现他们的方案的核心思路

1. 插件自身负责服务静态文件
2. 插件/operator通过 CRD 负责声明提供的是哪个插件，文件在哪
3. console-operator 负责基于 CRD 生成 configmap 供 console 页面消费

#### 1. 插件自身负责服务静态文件

通过 Openshift Pipelines Console Plugin 我们可以看到它提供的 Chart 如何实际提供静态文件

- [Console plugin Chart 中的 Deployment](https://github.com/openshift-pipelines/console-plugin/blob/main/charts/openshift-console-plugin/templates/deployment.yaml)

OCP 的每个 console-plugin 是基于一个[模版仓库](https://github.com/openshift/console-plugin-template)开始的，并且如上 Chart 也在[模版中维护](https://github.com/openshift/console-plugin-template/tree/main/charts/openshift-console-plugin)


这个 chart 渲染后会同步到 operator 的代码仓库中

- [Operator 代码仓库中的 console plugin](https://github.com/openshift-pipelines/operator/blob/main/.konflux/olm-catalog/bundle/kodata/static/tekton-config/00-console-plugin/pipeline_console_plugin.yaml)

里面的内容也非常的简单:

- [Service](https://github.com/openshift/console-plugin-template/blob/main/charts/openshift-console-plugin/templates/service.yaml): 暴露服务
- [ConfigMap](https://github.com/openshift/console-plugin-template/blob/main/charts/openshift-console-plugin/templates/configmap.yaml): 保存 nginx 配置
- [Deployment](https://github.com/openshift/console-plugin-template/blob/main/charts/openshift-console-plugin/templates/deployment.yaml): 部署动态插件静态文件服务器
- [ConsolePlugin](https://github.com/openshift/console-plugin-template/blob/main/charts/openshift-console-plugin/templates/consoleplugin.yaml): 如上提到的动态插件的 CRD
- 其他：根据需求插件也可以提供更复杂的功能，比如 `Job`, `ClusterRole` 等

#### 2. 插件/operator通过 CRD 负责声明提供的是哪个插件，文件在哪

这是 Operator 通过如上 console-plugin 的 Chart 提供 [ConsolePlugin](https://github.com/openshift/console-plugin-template/blob/main/charts/openshift-console-plugin/templates/consoleplugin.yaml) CRD 的内容

此 [ConsolePlugin CRD](https://github.com/openshift/api/blob/master/console/v1/types_console_plugin.go#L22) 的由于 console-operator 来维护并同步

- [consoleOperator struct](https://github.com/openshift/console-operator/blob/1bcdb7813905b47d55dd1417a4c970f70caf75f3/pkg/console/operator/operator.go#L59)
- [同步逻辑](https://github.com/openshift/console-operator/blob/1bcdb7813905b47d55dd1417a4c970f70caf75f3/pkg/console/operator/sync_v400.go)


#### 3. console-operator 负责基于 CRD 生成 configmap 供 console 页面消费

整个同步入口从 [这里开始](https://github.com/openshift/console-operator/blob/main/pkg/console/operator/operator.go#L312)

TODO: 深度研究逻辑


#### 4. console 如何消费 configmap 内容

OCP console 使用 Server Side Rendering 来解决主应用是如何获取环境信息/配置

- 通过 [jsGlobals](https://github.com/openshift/console/blob/main/pkg/server/server.go#L93) 记录所有的配置项
- 请求 index.html 时渲染所需要的配置 [见 index handler](https://github.com/openshift/console/blob/main/pkg/server/server.go#L688)
- 在 index.html 中把所有的配置[以 js 变量的方式](https://github.com/openshift/console/blob/main/frontend/public/index.html#L62)提供 `window.SERVER_FLAGS = [[.ServerFlags]]`


## 提案

参考 OCP 的做法，提供一个统一的 chart 不同的业务可以自己纳入到流程中，这里分成三部分

1. console 期望的信息：`plugins-config`
2. 插件/operator 如何自己部署动态插件服务器: `console-plugin` chart
3. console-operator 通过某CRD来发现并同步 `console-plugin` 信息到 `plugins-config`



### 1. Console `plugins-config`

参考 Openshift 的配置方案：

```yaml
data:
  <plugin-name>: <plugin-url>
```

例如

```yaml
data:
  alauda-devops-pipelines: http://console-plugin.tekton-operator.svc.cluster.local
```

### 2. `console-plugin` chart

#### 2.1 chart

跟 OCP 相同思路: [Chart](https://github.com/openshift/console-plugin-template/tree/main/charts/openshift-console-plugin)

针对不同的版本/部署方式提供一个统一的 `console-plugin` chart。里面提供部署静态文件服务器的模板以及所需要的默认配置。

例如：


```bash
console-plugin
├── Chart.yaml
├── charts
├── templates
│   ├── _helpers.tpl
│   ├── deployment.yaml
│   ├── hpa.yaml
│   ├── ingress.yaml
│   ├── NOTES.txt
│   ├── service.yaml
│   ├── serviceaccount.yaml
│   └── tests
│       └── test-connection.yaml
└── values.yaml
```

此 chart 可以统一维护，也可以有自己的版本管理和迭代，如果在某些具体的 ACP 版本需要引入新的配置并且是一个不可兼容的改动一样的可以通过添加新的版本号，`2.y.z` 来提供方案。

通过如下步骤构建并推送到 harbor 仓库：


```bash
# Build helm tar
helm package console-plugin
# $ Successfully packaged chart and saved it to: [...]/console-plugin-4.0.0.tgz
## builds: console-plugin-4.0.0.tgz

helm push console-plugin-4.0.0.tgz oci://build-harbor.alauda.cn/console-plugin/
# $ Pushed: build-harbor.alauda.cn/console-plugin/console-plugin:4.0.0
# $ Digest: sha256:6c4b23a3abe15f43253da3ebbd8a0f3bfb4471b3950a523dcdc47392ad6d51fc
```

*PS: When using oci repositories with helm it is necessary to omit the repository part of the artifact, since it will automatically use the chart's name for it*

那么 ACP 插件可以考虑如下几个选择。如下补充每个选择的使用场景：

1. **Parent chart**: 通过添加 `dependencies` 添加 `console-plugin` chart 的依赖
2. **Static manifests**: 通过 `helm template` 等命令生成目标静态 `yaml`


##### 2.1.1 Parent chart

通过添加 `console-plugin` helm chart 依赖解决静态插件部署的需求：

**场景**

1. **Operator 部署其他 helm chart**: 在 Helm `Chart.yaml` 中添加 `console-plugin` 的依赖，选择符合自己场景的版本，提供所需要的配置.


**Chart.yaml**:

```yaml
# [...]
dependencies:
- name: console-plugin
  # OCI repository
  # NOTE: the repository part is added automatically by helm using the `name` attribute
  repository: oci://build-harbor.alauda/console-plugin
  version: 4.0.0
```

**values.yaml**:

```yaml
console-plugin:
  # any customizations for the console-plugin chart values
  image:
    repository: build-harbor.alauda.cn/alauda-devops-pipelines/console-plugin
    tag: 4.2.0
```

2. **Plugin umbrella chart**: 在前端仓库中提供一个 chart 来单独维护所需要的配置并单独提供自己的 chart

在某些场景下，因为 UI 插件的特殊性需要自己提供额外的配置或 kubernetes resources, 那么可以搭建自己的 Chart 并添加 `console-plugin` chart 的依赖来提供更复杂的部署场景

```shell
pipeline-console-plugin/
├── Chart.yaml
├── templates
│   └── my-custom-resource.yaml
└── values.yaml
```


##### 2.1.2 Static manifests

UI 插件可以在自己的仓库和流水线中提供构建静态 `yaml` 文件来让 operator 使用来部署  UI 插件

例如

- `helm template`: 在流水线和仓库中提供直接渲染最终 `yaml` 的命令


**流水线**:

```mermaid
graph LR
  A[git-clone] --> B
  B[nodejs build] --> C
  C[Image build] --> D
  D[Helm template/package] --> E[Upload to nexus/harbor]
```


**渲染 chart 命令**:

```shell
helm template <release-name> oci://build-harbor.alauda.cn/console-plugin/console-plugin --version 4.0.0 [--set image.tag=4.1.0  --set image.repository=build-harbor.alauda.cn/alauda-devops-pipeline/console-plugin]
```

**场景**:

Operator 本身直接用静涛的 manifests 来进行部署，并且具备 patch 等能力



#### 2.2 镜像

这里分别考虑两种思路

1. **Server as base image**: 静态服务器跟静态文件一起打包，并默认作为 UI 插件的基础镜像
2. **Busybox as base image**: 静态服务器的镜像以及配置都统一在 `console-plugin` chart 中维护，UI 插件镜像只需要有 `busybox` + 静态文件

##### 2.2.1 Server as base image

这个方案所有的插件是基于一个统一的基础镜像构建，如下一个例子


```dockerfile
FROM build-harbor.alauda.cn/console-plugin/base-image:v1.0.0

COPY ./dist/ /app/dist
```

或者用 `ko` (`TODO: 待验证能否直接构建远程程序)


**优点**:

- **直接简单**: 部署方案比较简单，支持直接 `docker run`
- **Dockerfile 简单**: 也可以用 `ko` 来简化整个构建过程

**缺点**:

- **漏洞**: UI插件镜像的漏洞变多，UI 插件开发着需要及时关注漏洞情况并修改基础镜像的版本


##### 2.2.2 Busybox as base image

像现在方案类似，UI 插件镜像仅提供静态文件和简单的 shell 环境，并在部署通过 `console-plugin` chart 来部署静态服务器和处理如何访问静态文件

比如

 - `initContainer`: 通过 shell 复制内容到一个 `emptyDir`
 - `volume` 和 `sidecar`: 通过共享目录解决服务器如何访问静态镜像内容 (`TODO: 待测试验证`)


**优点**:

- **熟悉方案**: 当前 ACP 控制台使用类似方案
- **漏洞**: Server 引入的漏洞可以统一在 `console-plugin` chart 中解决，只需要更新一下新的版本号或者通过

**缺点**:

- **增加部署复杂度**: UI 插件的镜像无法独立使用，需要额外的命令和配置才能



#### 2.3 同步到 operator

TODO: 补充

> 大致思路是插件产出是镜像 + yaml 或者 chart
> operator 可以选择：
>  - 直接用yaml和镜像部署
>  - 包装 chart 来部署


